---
- name: Create VM in Proxmox VE
  hosts: localhost
  gather_facts: false
  vars:
    api_user: "root@pam"
    api_password: "YOUR_PASSWORD"
    api_host: "proxmox.example.com"
    vmid: 100
    vm_name: "myvm"
    iso_path: "local:iso/debian-12.iso"
    storage: "local-lvm"
    disk_size: 10
    memory: 2048
    cores: 2

  tasks:
    - name: Create VM
      community.general.proxmox_kvm:
        api_user: "{{ api_user }}"
        api_password: "{{ api_password }}"
        api_host: "{{ api_host }}"
        vmid: "{{ vmid }}"
        name: "{{ vm_name }}"
        cores: "{{ cores }}"
        memory: "{{ memory }}"
        scsihw: "virtio-scsi-pci"
        scsi:
          scsi0: "{{ storage }}:{{ disk_size }}"
        ide:
          ide2: "{{ iso_path }},media=cdrom"
        net:
          net0: "virtio,bridge=vmbr0"
        boot: "order=scsi0;ide2"
        state: present
        timeout: 300
